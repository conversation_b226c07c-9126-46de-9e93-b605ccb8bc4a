package eu.torvian.chatbot.app.compose.chatarea

import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import eu.torvian.chatbot.app.domain.contracts.ChatAreaDialogState

/**
 * Manages displaying dialogs for the Chat Area based on the ViewModel's state.
 *
 * @param dialogState The current dialog state from the ViewModel.
 */
@Composable
fun Dialogs(dialogState: ChatAreaDialogState) {
    when (dialogState) {
        is ChatAreaDialogState.DeleteMessage -> {
            DeleteMessageDialog(
                onDeleteConfirm = dialogState.onDeleteConfirm,
                onDismiss = dialogState.onDismiss
            )
        }
        ChatAreaDialogState.None -> { /* No dialog to show */ }
    }
}

/**
 * Dialog for confirming message deletion.
 */
@Composable
private fun DeleteMessageDialog(
    onDeleteConfirm: () -> Unit,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Delete Message?") },
        text = { Text("Are you sure you want to delete this message? This action cannot be undone.") },
        confirmButton = {
            Button(
                onClick = onDeleteConfirm,
                colors = ButtonDefaults.buttonColors(containerColor = MaterialTheme.colorScheme.error)
            ) { Text("Delete") }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
}
